<template>
  <div class="wrap">
    <div class="card-shadow"></div>
    <div class="recharge-card" :class="cardClasses" :style="cardStyle" @click="handleClick">
      <!-- 图标区域 -->
      <div class="icon-wrapper">
        <WithdrawTypeIcon
          :type="item.name"
          :icon="item.icon"
          :width="iconSize"
          :height="iconSize"
        />
      </div>

      <!-- 名称区域 -->
      <div class="name-wrapper">
        <AutoResizeText
          :text="item.name"
          :container-width="50"
          :container-height="36"
          :max-font-size="16"
          :min-font-size="10"
          :multi-line="true"
          :max-lines="2"
          :line-height="1.2"
          :padding="2"
        />
      </div>

      <!-- 选中状态图标 -->
      <div class="check-wrapper">
        <CheckedUnCheckedIcon :type="item.name" :isChecked="isSelected" :size="checkIconSize" />
      </div>

      <!-- 选中状态指示器 -->
      <div v-if="isSelected" class="selected-indicator" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useDepositStore } from "@/stores/deposit";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import CheckedUnCheckedIcon from "@/components/ZComonImg/CheckedUnCheckedIcon.vue";
import WithdrawTypeIcon from "@/components/ZComonImg/WithdrawTypeIcon.vue";
import AutoResizeText from "@/components/AutoResizeText/index.vue";

// 类型定义
interface RechargeItem {
  name: string;
  icon?: string;
  [key: string]: any;
}

interface Props {
  /** 充值项目数据 */
  item: RechargeItem;
}

const props = defineProps<Props>();

// Store
const depositStore = useDepositStore();
const { curRechangeMethods } = storeToRefs(depositStore);

// 常量
const CARD_THEMES = {
  gcash: {
    background: "linear-gradient(90deg, #39B0FF 0%, #4881ED 100%)",
    checkBoxColor: "#004CDD",
    name: "Gcash",
    textColor: "#fff",
  },
  maya: {
    background: "linear-gradient(90deg, #4BDE5E 0%, #00C763 100%)",
    checkBoxColor: "#00A74C",
    name: "Maya",
    textColor: "#fff",
  },
  bank: {
    background: "linear-gradient(90deg, #FFB700 0%, #FF9500 100%)",
    checkBoxColor: "#E16500",
    name: "Default",
    textColor: "#fff",
  },
  default: {
    background: "linear-gradient(90deg, #00D3AD 0%, #00BB95 100%)",
    checkBoxColor: "#009072",
    name: "Default",
    textColor: "#fff",
  },
  default1: {
    background: "linear-gradient(90deg, #FF7878 0%, #FF4243 100%)",
    checkBoxColor: "#D20A0A",
    name: "Default",
    textColor: "#fff",
  },
  default2: {
    background: "#01d46a",
    checkBoxColor: "#00A74C",
    name: "Default",
    textColor: "#fff",
  },
} as const;
// 计算属性
const isSelected = computed(() => curRechangeMethods.value === props.item.name);

const cardTheme = computed(() => {
  return CARD_THEMES[props.item.name?.toLocaleLowerCase()] || CARD_THEMES.default;
});

const cardClasses = computed(() => ({
  "is-selected": isSelected.value,
  "is-gcash": props.item.name === CHANEL_TYPE.G_CASH,
  "is-maya": props.item.name === CHANEL_TYPE.MAYA,
  "is-clickable": !isSelected.value,
}));

const cardStyle = computed(() => ({
  background: cardTheme.value.background,
  color: cardTheme.value.textColor,
}));

const iconSize = computed(() => 23);
const checkIconSize = computed(() => 20);

// 方法
const handleClick = () => {
  if (isSelected.value) return;

  depositStore.setCurReChangeName(props.item.name);
};
</script>

<style lang="scss" scoped>
.wrap {
  position: relative;
  background: transparent;
  padding: 0;
  margin: 0;
  transition: all 0.2s ease;
  .card-shadow {
    position: absolute;
    width: 92px;
    height: 56px;
    border-radius: 16px;
    top: 0;
    left: 8px;
    z-index: 1;
    background: transparent;
    box-shadow: 0 4px 8px 0 #333; // TODO 颜色调整
  }

  &:hover {
    transform: translateY(-1px);
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
.recharge-card {
  width: 108px;
  height: 56px;
  max-height: 56px;
  min-height: 56px;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 6px 8px;
  color: #fff;
  position: relative;
  z-index: 5;
  cursor: pointer;
  box-sizing: border-box;
  overflow: hidden;

  &.is-selected {
    border-color: rgba(255, 255, 255, 0.3);
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
  }

  .name-wrapper {
    flex: 1;
    text-align: left;
  }

  .check-wrapper {
    position: absolute;
    right: 4px;
    bottom: 6px;
  }

  .selected-indicator {
    display: none;
  }
}
</style>
