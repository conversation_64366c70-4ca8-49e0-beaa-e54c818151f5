<template>
  <div
    class="card-wrap"
    :style="{
      background: cardInfo.backgroundColor,
      boxShadow: `0 10px 15px 0 ${cardInfo.backgroundColor}4d`,
    }"
    @click="handleClickCard"
  >
    <div class="card-header">
      <div class="left">
        <div class="card-logo">
          <WithdrawTypeIcon :icon="cardInfo.icon" />
        </div>
        <span class="card-name">{{ cardInfo.name }}</span>
      </div>
      <div class="right">
        <template v-if="status === Status.CHECK">
          <CheckedUnCheckedIcon
            :disabled="!activeChannel"
            :type="cardInfo.name"
            :isChecked="checkedAccountId === item.account_id"
          />
        </template>
        <ZIcon v-if="status === Status.EDIT" type="icon-bianji1" color="#fff" />
      </div>
    </div>
    <div class="card-footer">
      <span class="card-num">{{ formatCardNumber(item.account_no) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Status, Item } from "./type";
import { formatCardNumber } from "@/utils/core/tools";
import { getMethodsInfo, METHODS_NAMES } from "@/utils/config/GlobalConstant";
import { showToast } from "vant";
import { useWithdrawStore } from "@/stores/withdraw";
import WithdrawTypeIcon from "@/components/ZComonImg/WithdrawTypeIcon.vue";

const emits = defineEmits(["click"]);

const props = defineProps({
  item: {
    type: Object as () => Item,
    required: true,
    default: () => ({}),
  },
  status: {
    type: String as () => Status,
    default: Status.VIEW,
    validator: (value: string) => Object.values(Status).includes(value as Status),
  },
  checkedAccountId: {
    type: String,
    default: "",
  },
});

const activeChannel = computed(() => {
  const withdrawStore = useWithdrawStore();
  const list = withdrawStore.withdrawData || [];
  const activeChannel = list.find((item) => item.account_type === props.item.type);
  return activeChannel;
});

const cardInfo = computed(() => {
  const base = getMethodsInfo(props.item?.type);
  return {
    name: base.name,
    backgroundColor: activeChannel.value ? base.activeColor : base.unActiveColor,
    icon: activeChannel.value?.icon,
  };
});

const handleClickCard = () => {
  if (props.status === Status.VIEW) return;
  if (!activeChannel.value) {
    showToast(
      `${METHODS_NAMES[props.item.type]} cannot withdraw, please switch to other withdrawal methods`
    );
    return;
  }
  emits("click", props.item);
};
</script>

<style scoped lang="scss">
.card-wrap {
  width: 100%;
  height: 160px;
  border-radius: 28px;
  color: #fff;
  padding: 20px;
  box-sizing: border-box;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  box-shadow: rgba(64, 134, 244, 0.35) 0px 10px 15px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .left {
      display: flex;
      align-items: center;

      .card-logo {
        margin-right: 10px;
      }

      .card-name {
        color: #fff;
        font-size: 18px;
        font-weight: 400;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }

  .card-footer {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;

    .card-num {
      color: #fff;
      text-align: center;
      font-family: D-DIN;
      font-size: 32px;
      font-weight: 700;
    }
  }
}
</style>
